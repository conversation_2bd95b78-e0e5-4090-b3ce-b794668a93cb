'use client';
import React from 'react';

interface FieldOption {
  value: string;
  label: string;
}

interface Props {
  headers: string[];
  mapping: Record<string, string>;
  fields: FieldOption[];
  onChange: (header: string, value: string) => void;
  onNext: () => void;
}

export default function MappingStep({ headers, mapping, fields, onChange, onNext }: Props) {
  return (
    <div style={{
      backgroundColor: '#ffffff',
      borderRadius: '12px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      padding: '32px 24px',
      color: '#000000'
    }}>
      <h2 style={{
        fontSize: '20px',
        fontWeight: 'bold',
        marginBottom: '16px',
        color: '#000000'
      }}>
        🗺️ Map fields
      </h2>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {headers.map((h) => (
          <div key={h} style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            padding: '8px',
            backgroundColor: '#f8f9fa',
            borderRadius: '6px'
          }}>
            <span style={{
              width: '160px',
              fontSize: '14px',
              fontWeight: 'bold',
              color: '#000000'
            }}>
              {h}
            </span>
            <select
              value={mapping[h] || ''}
              onChange={(e) => onChange(h, e.target.value)}
              style={{
                border: '2px solid #000000',
                padding: '8px',
                borderRadius: '4px',
                flex: '1',
                fontSize: '14px',
                color: '#000000',
                backgroundColor: '#ffffff'
              }}
            >
              {fields.map((f) => (
                <option key={f.value} value={f.value} style={{ color: '#000000' }}>
                  {f.label}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>
      <div style={{ marginTop: '24px', textAlign: 'right' }}>
        <button
          style={{
            padding: '12px 24px',
            backgroundColor: '#4f46e5',
            color: '#ffffff',
            border: 'none',
            borderRadius: '6px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: 'pointer'
          }}
          onClick={onNext}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = '#3730a3';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = '#4f46e5';
          }}
        >
          Next →
        </button>
      </div>
    </div>
  );
}
