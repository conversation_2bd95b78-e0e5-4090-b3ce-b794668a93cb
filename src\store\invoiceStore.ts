import { create } from "zustand";

export type ClientData = {
  firstName?: string;
  lastName?: string;
  companyName?: string;
  address?: string;
  vat?: string;
  email?: string;
  phone?: string;
};

export type CompanyData = {
  companyName?: string;
  address?: string;
  vat?: string;
  email?: string;
  phone?: string;
};

export type Service = {
  description: string;
  price: number;
};

export type InvoiceData = {
  client: ClientData;
  company: CompanyData;
  services: Service[];
  vatPercent?: number;
  logo?: string; // base64
  layout?: string;
  colorScheme?: string;
  invoiceNumber?: string;
  invoiceDate?: string;
  currency?: string;
  paymentInfo?: string;
  category?: string;
};

type InvoiceStore = {
  step: number;
  data: InvoiceData;
  addClientData: (input: Partial<ClientData>) => void;
  addCompanyData: (input: Partial<CompanyData>) => void;
  addService: (service: Service) => void;
  removeService: (index: number) => void;
  setVatPercent: (percent?: number) => void;
  setLogo: (logo: string) => void;
  setLayout: (layout: string) => void;
  setColorScheme: (colorScheme: string) => void;
  setInvoiceNumber: (num: string) => void;
  setInvoiceDate: (date: string) => void;
  setCurrency: (currency: string) => void;
  setPaymentInfo: (info: string) => void;
  setCategory: (category: string) => void;
  nextStep: () => void;
  prevStep: () => void;
  reset: () => void;
};

const initialData: InvoiceData = {
  client: {},
  company: {},
  services: [],
  currency: "€",
  category: undefined,
};

export const useInvoiceStore = create<InvoiceStore>((set) => ({
  step: 0,
  data: { ...initialData },
  addClientData: (input) => set((state) => ({
    data: { ...state.data, client: { ...state.data.client, ...input } },
  })),
  addCompanyData: (input) => set((state) => ({
    data: { ...state.data, company: { ...state.data.company, ...input } },
  })),
  addService: (service) => set((state) => ({
    data: { ...state.data, services: [...state.data.services, service] },
  })),
  removeService: (index) => set((state) => ({
    data: { ...state.data, services: state.data.services.filter((_, i) => i !== index) },
  })),
  setVatPercent: (percent) => set((state) => ({
    data: { ...state.data, vatPercent: percent },
  })),
  setLogo: (logo) => set((state) => ({
    data: { ...state.data, logo },
  })),
  setLayout: (layout) => set((state) => ({
    data: { ...state.data, layout },
  })),
  setColorScheme: (colorScheme) => set((state) => ({
    data: { ...state.data, colorScheme },
  })),
  setInvoiceNumber: (num) => set((state) => ({
    data: { ...state.data, invoiceNumber: num },
  })),
  setInvoiceDate: (date) => set((state) => ({
    data: { ...state.data, invoiceDate: date },
  })),
  setCurrency: (currency) => set((state) => ({
    data: { ...state.data, currency },
  })),
  setPaymentInfo: (paymentInfo) => set((state) => ({
    data: { ...state.data, paymentInfo },
  })),
  setCategory: (category) => set((state) => ({
    data: { ...state.data, category },
  })),
  nextStep: () => set((state) => ({ step: state.step + 1 })),
  prevStep: () => set((state) => ({ step: Math.max(0, state.step - 1) })),
  reset: () => set({ step: 0, data: { ...initialData } }),
}));
