'use client'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '../../../lib/supabaseClient'
import { useAuth } from '../../../components/AuthProvider'

export default function HistoryPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [invoices, setInvoices] = useState<{ id: number; invoice_number: string | null; category: string | null; layout: string; created_at: string }[]>([])

  useEffect(() => {
    if (!user) {
      router.push('/account/login')
      return
    }
    supabase
      .from('invoices')
      .select('id, invoice_number, category, layout, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .then(({ data }) => {
        setInvoices(data || [])
      })
  }, [user, router])

  if (!user) return null

  return (
    <div className="w-full max-w-3xl mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-indigo-700">Invoice History</h1>
      <ul className="space-y-2">
        {invoices.map((inv) => (
          <li key={inv.id} className="border p-4 rounded-md bg-white flex justify-between">
            <span>{inv.invoice_number || 'Invoice'} - {inv.category || 'no category'}</span>
            <span className="text-sm text-gray-500">{new Date(inv.created_at).toLocaleDateString()}</span>
          </li>
        ))}
        {invoices.length === 0 && <p>No invoices yet.</p>}
      </ul>
    </div>
  )
}
