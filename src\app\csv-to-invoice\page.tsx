'use client';
import React, { useState } from 'react';
import LoadingOverlay from '../../components/LoadingOverlay';
import UploadStep from '../../components/csvToInvoice/UploadStep';
import MappingStep from '../../components/csvToInvoice/MappingStep';
import LayoutStep from '../../components/wizard/LayoutStep';
import type { InvoiceData, ClientData, CompanyData } from '../../store/invoiceStore';
import { parseCsv, createZip } from '../../lib/csv';
import { LayoutType } from '../../components/wizard/types';

const fields = [
  { value: '', label: 'Ignore' },
  { value: 'client.firstName', label: 'Client first name' },
  { value: 'client.lastName', label: 'Client last name' },
  { value: 'client.companyName', label: 'Client company name' },
  { value: 'client.address', label: 'Client address' },
  { value: 'client.vat', label: 'Client VAT' },
  { value: 'client.email', label: 'Client email' },
  { value: 'client.phone', label: 'Client phone' },
  { value: 'company.companyName', label: 'Your company name' },
  { value: 'company.address', label: 'Your company address' },
  { value: 'company.vat', label: 'Your company VAT' },
  { value: 'company.email', label: 'Your company email' },
  { value: 'company.phone', label: 'Your company phone' },
  { value: 'service.description', label: 'Service description' },
  { value: 'service.price', label: 'Service price' },
  { value: 'invoiceNumber', label: 'Invoice number' },
  { value: 'invoiceDate', label: 'Invoice date' },
  { value: 'currency', label: 'Currency' },
  { value: 'vatPercent', label: 'VAT percent' },
];

type Step = 'upload' | 'mapping' | 'layout';

export default function CsvToInvoicePage() {
  const [step, setStep] = useState<Step>('upload');
  const [headers, setHeaders] = useState<string[]>([]);
  const [rows, setRows] = useState<string[][]>([]);
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [layout, setLayout] = useState<LayoutType>('classic');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = String(e.target?.result || '');
      const data = parseCsv(text);
      if (data.length > 0) {
        setHeaders(data[0]);
        setRows(data.slice(1));
        setStep('mapping');
      }
    };
    reader.readAsText(file);
  };

  const buildData = (row: string[]): InvoiceData => {
    const data: InvoiceData = { client: {}, company: {}, services: [] } as InvoiceData;
    let serviceDesc = '';
    let servicePrice = '';
    headers.forEach((h, idx) => {
      const field = mapping[h];
      if (!field) return;
      const val = row[idx];
      if (field.startsWith('client.')) {
        const key = field.split('.')[1] as keyof ClientData;
        (data.client as ClientData)[key] = val;
      } else if (field.startsWith('company.')) {
        const key = field.split('.')[1] as keyof CompanyData;
        (data.company as CompanyData)[key] = val;
      } else if (field === 'service.description') {
        serviceDesc = val;
      } else if (field === 'service.price') {
        servicePrice = val;
      } else if (field === 'invoiceNumber') {
        data.invoiceNumber = val;
      } else if (field === 'invoiceDate') {
        data.invoiceDate = val;
      } else if (field === 'currency') {
        data.currency = val;
      } else if (field === 'vatPercent') {
        data.vatPercent = val ? parseFloat(val) : undefined;
      }
    });
    if (serviceDesc) {
      data.services.push({ description: serviceDesc, price: parseFloat(servicePrice || '0') });
    }
    return data;
  };

  const download = (name: string, blob: Blob) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = name;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      URL.revokeObjectURL(url);
      a.remove();
    }, 200);
  };

  const handleGenerate = async () => {
    setLoading(true);
    setError(null);
    try {
      const files: { name: string; data: Uint8Array }[] = [];
      for (let i = 0; i < rows.length; i++) {
        const invoiceData = buildData(rows[i]);
        const res = await fetch('/api/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ data: invoiceData, layout }),
        });
        if (!res.ok) throw new Error('PDF generation failed');
        const arr = new Uint8Array(await res.arrayBuffer());
        files.push({ name: `invoice-${i + 1}.pdf`, data: arr });
      }
      if (files.length === 1) {
        download(files[0].name, new Blob([files[0].data], { type: 'application/pdf' }));
      } else {
        const zip = createZip(files);
        download('invoices.zip', zip);
      }
    } catch (e: unknown) {
      const err = e instanceof Error ? e.message : String(e);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto py-10 px-4">
      {loading && <LoadingOverlay />}
      {step === 'upload' && <UploadStep onFile={handleFile} />}
      {step === 'mapping' && (
        <MappingStep
          headers={headers}
          mapping={mapping}
          fields={fields}
          onChange={(header, value) => setMapping({ ...mapping, [header]: value })}
          onNext={() => setStep('layout')}
        />
      )}
      {step === 'layout' && (
        <div className="bg-white rounded-xl shadow px-6 py-8 text-center">
          <h2 className="text-xl font-semibold mb-4 text-indigo-700 dark:text-indigo-300">Choose template</h2>
          <LayoutStep selected={layout} onSelect={setLayout} />
          {error && <p className="text-red-500 mt-4">{error}</p>}
          <button
            className="mt-6 px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
            onClick={handleGenerate}
          >
            Generate
          </button>
        </div>
      )}
    </div>
  );
}
