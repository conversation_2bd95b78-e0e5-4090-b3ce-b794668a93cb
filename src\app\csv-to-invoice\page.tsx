'use client';
import React, { useState } from 'react';
import LoadingOverlay from '../../components/LoadingOverlay';
import UploadStep from '../../components/csvToInvoice/UploadStep';
import MappingStep from '../../components/csvToInvoice/MappingStep';
import LayoutStep from '../../components/wizard/LayoutStep';
import type { InvoiceData, ClientData, CompanyData } from '../../store/invoiceStore';
import { parseCsv, createZip } from '../../lib/csv';
import { LayoutType } from '../../components/wizard/types';

const fields = [
  { value: '', label: 'Ignore' },
  { value: 'client.firstName', label: 'Client first name' },
  { value: 'client.lastName', label: 'Client last name' },
  { value: 'client.companyName', label: 'Client company name' },
  { value: 'client.address', label: 'Client address' },
  { value: 'client.vat', label: 'Client VAT' },
  { value: 'client.email', label: 'Client email' },
  { value: 'client.phone', label: 'Client phone' },
  { value: 'company.companyName', label: 'Your company name' },
  { value: 'company.address', label: 'Your company address' },
  { value: 'company.vat', label: 'Your company VAT' },
  { value: 'company.email', label: 'Your company email' },
  { value: 'company.phone', label: 'Your company phone' },
  { value: 'service.description', label: 'Service description' },
  { value: 'service.price', label: 'Service price' },
  { value: 'invoiceNumber', label: 'Invoice number' },
  { value: 'invoiceDate', label: 'Invoice date' },
  { value: 'currency', label: 'Currency' },
  { value: 'vatPercent', label: 'VAT percent' },
];

type Step = 'upload' | 'mapping' | 'layout';

export default function CsvToInvoicePage() {
  const [step, setStep] = useState<Step>('upload');
  const [headers, setHeaders] = useState<string[]>([]);
  const [rows, setRows] = useState<string[][]>([]);
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [layout, setLayout] = useState<LayoutType>('classic');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFile = (file: File) => {
    setError(null);
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = String(e.target?.result || '');
        if (!text.trim()) {
          setError('Il file CSV è vuoto');
          return;
        }

        const data = parseCsv(text);
        if (data.length === 0) {
          setError('Il file CSV non contiene dati validi');
          return;
        }

        if (data.length === 1) {
          setError('Il file CSV contiene solo l\'header. Aggiungi almeno una riga di dati.');
          return;
        }

        setHeaders(data[0]);
        setRows(data.slice(1));
        setStep('mapping');
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : 'Errore nel parsing del CSV';
        setError(`Errore nella lettura del file: ${errorMsg}`);
      }
    };

    reader.onerror = () => {
      setError('Errore nella lettura del file. Assicurati che sia un file CSV valido.');
    };

    reader.readAsText(file);
  };

  const buildData = (row: string[]): InvoiceData => {
    const data: InvoiceData = { client: {}, company: {}, services: [] } as InvoiceData;
    let serviceDesc = '';
    let servicePrice = '';

    headers.forEach((h, idx) => {
      const field = mapping[h];
      if (!field) return;

      // Assicurati che l'indice sia valido
      const val = idx < row.length ? row[idx]?.trim() || '' : '';

      if (field.startsWith('client.')) {
        const key = field.split('.')[1] as keyof ClientData;
        (data.client as ClientData)[key] = val;
      } else if (field.startsWith('company.')) {
        const key = field.split('.')[1] as keyof CompanyData;
        (data.company as CompanyData)[key] = val;
      } else if (field === 'service.description') {
        serviceDesc = val;
      } else if (field === 'service.price') {
        servicePrice = val;
      } else if (field === 'invoiceNumber') {
        data.invoiceNumber = val;
      } else if (field === 'invoiceDate') {
        data.invoiceDate = val;
      } else if (field === 'currency') {
        data.currency = val || 'EUR';
      } else if (field === 'vatPercent') {
        const parsed = val ? parseFloat(val.replace('%', '').replace(',', '.')) : undefined;
        data.vatPercent = !isNaN(parsed || 0) ? parsed : undefined;
      }
    });

    // Aggiungi il servizio solo se ha una descrizione
    if (serviceDesc) {
      const price = servicePrice ? parseFloat(servicePrice.replace(',', '.')) : 0;
      data.services.push({
        description: serviceDesc,
        price: !isNaN(price) ? price : 0
      });
    }

    // Valori di default se mancanti
    if (!data.currency) data.currency = 'EUR';
    if (!data.invoiceDate) data.invoiceDate = new Date().toISOString().split('T')[0];
    if (!data.invoiceNumber) data.invoiceNumber = `INV-${Date.now()}`;

    return data;
  };

  const download = (name: string, blob: Blob) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = name;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      URL.revokeObjectURL(url);
      a.remove();
    }, 200);
  };

  const handleGenerate = async () => {
    setLoading(true);
    setError(null);
    try {
      // Validazione dei dati prima di iniziare
      if (rows.length === 0) {
        throw new Error('Nessun dato da processare. Assicurati di aver caricato un file CSV valido.');
      }

      // Verifica che almeno alcuni campi essenziali siano mappati
      const mappedFields = Object.values(mapping).filter(field => field && field !== '');
      if (mappedFields.length === 0) {
        throw new Error('Devi mappare almeno un campo per generare le fatture.');
      }

      const files: { name: string; data: Uint8Array }[] = [];
      for (let i = 0; i < rows.length; i++) {
        try {
          const invoiceData = buildData(rows[i]);
          const res = await fetch('/api/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ data: invoiceData, layout }),
          });

          if (!res.ok) {
            let errorMessage = `Errore nella generazione del PDF per la riga ${i + 1}`;
            try {
              const errorData = await res.json();
              if (errorData.details) {
                errorMessage += `: ${errorData.details}`;
              } else if (errorData.error) {
                errorMessage += `: ${errorData.error}`;
              }
            } catch {
              errorMessage += ` (Status: ${res.status})`;
            }
            throw new Error(errorMessage);
          }

          const arr = new Uint8Array(await res.arrayBuffer());
          files.push({ name: `invoice-${i + 1}.pdf`, data: arr });
        } catch (rowError) {
          const errorMsg = rowError instanceof Error ? rowError.message : String(rowError);
          throw new Error(`Errore nella riga ${i + 1}: ${errorMsg}`);
        }
      }

      if (files.length === 1) {
        download(files[0].name, new Blob([files[0].data], { type: 'application/pdf' }));
      } else {
        const zip = createZip(files);
        download('invoices.zip', zip);
      }
    } catch (e: unknown) {
      const err = e instanceof Error ? e.message : String(e);
      console.error('CSV to Invoice error:', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto py-10 px-4">
      {loading && <LoadingOverlay />}
      {step === 'upload' && (
        <div>
          <UploadStep onFile={handleFile} />
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800 font-medium text-sm">
                Errore nel caricamento del file:
              </p>
              <p className="text-red-700 text-sm mt-1">{error}</p>
            </div>
          )}
        </div>
      )}
      {step === 'mapping' && (
        <MappingStep
          headers={headers}
          mapping={mapping}
          fields={fields}
          onChange={(header, value) => setMapping({ ...mapping, [header]: value })}
          onNext={() => setStep('layout')}
        />
      )}
      {step === 'layout' && (
        <div className="bg-white rounded-xl shadow px-6 py-8 text-center">
          <h2 className="text-xl font-semibold mb-4 text-indigo-700 dark:text-indigo-300">Choose template</h2>
          <LayoutStep selected={layout} onSelect={setLayout} />
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800 font-medium text-sm">
                Errore durante la generazione:
              </p>
              <p className="text-red-700 text-sm mt-1">{error}</p>
            </div>
          )}
          <button
            className="mt-6 px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
            onClick={handleGenerate}
          >
            Generate
          </button>
        </div>
      )}
    </div>
  );
}
