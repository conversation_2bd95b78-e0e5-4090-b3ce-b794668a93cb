'use client';
import React, { useState } from 'react';
import LoadingOverlay from '../../components/LoadingOverlay';
import UploadStep from '../../components/csvToInvoice/UploadStep';
import MappingStep from '../../components/csvToInvoice/MappingStep';
import LayoutStep from '../../components/wizard/LayoutStep';
import type { InvoiceData, ClientData, CompanyData } from '../../store/invoiceStore';
import { parseCsv, createZip } from '../../lib/csv';
import { LayoutType } from '../../components/wizard/types';

const fields = [
  { value: '', label: 'Ignore' },
  { value: 'client.firstName', label: 'Client first name' },
  { value: 'client.lastName', label: 'Client last name' },
  { value: 'client.companyName', label: 'Client company name' },
  { value: 'client.address', label: 'Client address' },
  { value: 'client.vat', label: 'Client VAT' },
  { value: 'client.email', label: 'Client email' },
  { value: 'client.phone', label: 'Client phone' },
  { value: 'company.companyName', label: 'Your company name' },
  { value: 'company.address', label: 'Your company address' },
  { value: 'company.vat', label: 'Your company VAT' },
  { value: 'company.email', label: 'Your company email' },
  { value: 'company.phone', label: 'Your company phone' },
  { value: 'service.description', label: 'Service description' },
  { value: 'service.price', label: 'Service price' },
  { value: 'invoiceNumber', label: 'Invoice number' },
  { value: 'invoiceDate', label: 'Invoice date' },
  { value: 'currency', label: 'Currency' },
  { value: 'vatPercent', label: 'VAT percent' },
];

type Step = 'upload' | 'mapping' | 'layout';

export default function CsvToInvoicePage() {
  const [step, setStep] = useState<Step>('upload');
  const [headers, setHeaders] = useState<string[]>([]);
  const [rows, setRows] = useState<string[][]>([]);
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [layout, setLayout] = useState<LayoutType>('classic');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFile = (file: File) => {
    setError(null);
    console.log('Processing file:', file.name, 'Size:', file.size, 'Type:', file.type);

    // Validazione del file
    if (!file.name.toLowerCase().endsWith('.csv')) {
      setError('Il file deve avere estensione .csv');
      return;
    }

    if (file.size === 0) {
      setError('Il file è vuoto');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setError('Il file è troppo grande. Massimo 5MB consentiti.');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = String(e.target?.result || '');
        console.log('File content length:', text.length);
        console.log('First 200 chars:', text.substring(0, 200));

        if (!text.trim()) {
          setError('Il file CSV è vuoto');
          return;
        }

        const data = parseCsv(text);
        console.log('Parsed CSV data:', data.length, 'rows');
        console.log('Headers:', data[0]);

        if (data.length === 0) {
          setError('Il file CSV non contiene dati validi');
          return;
        }

        if (data.length === 1) {
          setError('Il file CSV contiene solo l\'header. Aggiungi almeno una riga di dati.');
          return;
        }

        setHeaders(data[0]);
        setRows(data.slice(1));
        setStep('mapping');
      } catch (err) {
        console.error('CSV parsing error:', err);
        const errorMsg = err instanceof Error ? err.message : 'Errore nel parsing del CSV';
        setError(`Errore nella lettura del file: ${errorMsg}`);
      }
    };

    reader.onerror = (err) => {
      console.error('FileReader error:', err);
      setError('Errore nella lettura del file. Assicurati che sia un file CSV valido.');
    };

    reader.readAsText(file, 'UTF-8');
  };

  const buildData = (row: string[]): InvoiceData => {
    console.log('🏗️ Building invoice data from row:', row);
    console.log('📋 Headers:', headers);
    console.log('🗺️ Mapping:', mapping);

    const data: InvoiceData = {
      client: {},
      company: {},
      services: [],
      currency: 'EUR',
      invoiceDate: new Date().toISOString().split('T')[0],
      invoiceNumber: `INV-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    } as InvoiceData;

    let serviceDesc = '';
    let servicePrice = '';

    try {
      headers.forEach((h, idx) => {
        const field = mapping[h];
        if (!field || field === '') return;

        // Assicurati che l'indice sia valido
        const val = idx < row.length ? (row[idx]?.trim() || '') : '';
        console.log(`🔄 Processing field: ${h} -> ${field} = "${val}"`);

        try {
          if (field.startsWith('client.')) {
            const key = field.split('.')[1] as keyof ClientData;
            if (key && val) {
              (data.client as ClientData)[key] = val;
              console.log(`👤 Set client.${key} = "${val}"`);
            }
          } else if (field.startsWith('company.')) {
            const key = field.split('.')[1] as keyof CompanyData;
            if (key && val) {
              (data.company as CompanyData)[key] = val;
              console.log(`🏢 Set company.${key} = "${val}"`);
            }
          } else if (field === 'service.description') {
            serviceDesc = val;
            console.log(`📝 Service description: "${val}"`);
          } else if (field === 'service.price') {
            servicePrice = val;
            console.log(`💰 Service price: "${val}"`);
          } else if (field === 'invoiceNumber' && val) {
            data.invoiceNumber = val;
            console.log(`🔢 Invoice number: "${val}"`);
          } else if (field === 'invoiceDate' && val) {
            data.invoiceDate = val;
            console.log(`📅 Invoice date: "${val}"`);
          } else if (field === 'currency' && val) {
            data.currency = val;
            console.log(`💱 Currency: "${val}"`);
          } else if (field === 'vatPercent' && val) {
            const cleanVal = val.replace('%', '').replace(',', '.').trim();
            const parsed = parseFloat(cleanVal);
            if (!isNaN(parsed)) {
              data.vatPercent = parsed;
              console.log(`📊 VAT percent: ${parsed}%`);
            }
          }
        } catch (fieldError) {
          console.error(`❌ Error processing field ${field}:`, fieldError);
        }
      });

      // Aggiungi il servizio solo se ha una descrizione
      if (serviceDesc) {
        const cleanPrice = servicePrice.replace(',', '.').trim();
        const price = cleanPrice ? parseFloat(cleanPrice) : 0;
        const finalPrice = !isNaN(price) ? price : 0;

        data.services.push({
          description: serviceDesc,
          price: finalPrice
        });
        console.log(`🛍️ Added service: "${serviceDesc}" - €${finalPrice}`);
      } else {
        console.warn('⚠️ No service description found, adding default service');
        data.services.push({
          description: 'Servizio',
          price: 0
        });
      }

      console.log('✅ Final invoice data:', JSON.stringify(data, null, 2));
      return data;

    } catch (error) {
      console.error('💥 Error in buildData:', error);
      throw new Error(`Errore nella costruzione dei dati della fattura: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const download = (name: string, blob: Blob) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = name;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      URL.revokeObjectURL(url);
      a.remove();
    }, 200);
  };

  const handleGenerate = async () => {
    console.log('🚀 Starting CSV to Invoice generation...');
    setLoading(true);
    setError(null);

    try {
      // Validazione dei dati prima di iniziare
      console.log('📊 Validating data...', { rowsCount: rows.length, mappingKeys: Object.keys(mapping) });

      if (rows.length === 0) {
        throw new Error('Nessun dato da processare. Assicurati di aver caricato un file CSV valido.');
      }

      // Verifica che almeno alcuni campi essenziali siano mappati
      const mappedFields = Object.values(mapping).filter(field => field && field !== '');
      console.log('🗺️ Mapped fields:', mappedFields);

      if (mappedFields.length === 0) {
        throw new Error('Devi mappare almeno un campo per generare le fatture.');
      }

      // Verifica che ci siano campi essenziali mappati
      const hasClientInfo = mappedFields.some(field => field.startsWith('client.'));
      const hasServiceInfo = mappedFields.some(field => field.startsWith('service.'));

      console.log('✅ Validation checks:', { hasClientInfo, hasServiceInfo });

      const files: { name: string; data: Uint8Array }[] = [];

      for (let i = 0; i < rows.length; i++) {
        console.log(`📄 Processing row ${i + 1}/${rows.length}:`, rows[i]);

        try {
          const invoiceData = buildData(rows[i]);
          console.log('🏗️ Built invoice data:', JSON.stringify(invoiceData, null, 2));

          // Validazione dei dati della fattura
          if (!invoiceData.client || Object.keys(invoiceData.client).length === 0) {
            console.warn('⚠️ No client data found for row', i + 1);
          }

          if (!invoiceData.services || invoiceData.services.length === 0) {
            console.warn('⚠️ No services found for row', i + 1);
          }

          console.log(`🌐 Sending API request for row ${i + 1}...`);
          const res = await fetch('/api/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ data: invoiceData, layout }),
          });

          console.log(`📡 API response for row ${i + 1}:`, { status: res.status, ok: res.ok });

          if (!res.ok) {
            let errorMessage = `Errore nella generazione del PDF per la riga ${i + 1}`;
            try {
              const errorData = await res.json();
              console.error('❌ API error data:', errorData);
              if (errorData.details) {
                errorMessage += `: ${errorData.details}`;
              } else if (errorData.error) {
                errorMessage += `: ${errorData.error}`;
              }
            } catch (parseError) {
              console.error('❌ Failed to parse error response:', parseError);
              errorMessage += ` (Status: ${res.status})`;
            }
            throw new Error(errorMessage);
          }

          const arr = new Uint8Array(await res.arrayBuffer());
          console.log(`✅ PDF generated for row ${i + 1}, size:`, arr.length);
          files.push({ name: `invoice-${i + 1}.pdf`, data: arr });

        } catch (rowError) {
          console.error(`❌ Error processing row ${i + 1}:`, rowError);
          const errorMsg = rowError instanceof Error ? rowError.message : String(rowError);
          throw new Error(`Errore nella riga ${i + 1}: ${errorMsg}`);
        }
      }

      console.log('📦 All PDFs generated, preparing download...', { fileCount: files.length });

      if (files.length === 1) {
        download(files[0].name, new Blob([files[0].data], { type: 'application/pdf' }));
      } else {
        const zip = createZip(files);
        download('invoices.zip', zip);
      }

      console.log('🎉 CSV to Invoice generation completed successfully!');

    } catch (e: unknown) {
      const err = e instanceof Error ? e.message : String(e);
      console.error('💥 CSV to Invoice error:', err, e);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto py-10 px-4">
      {loading && <LoadingOverlay />}
      {step === 'upload' && (
        <div>
          <UploadStep onFile={handleFile} />
          {error && (
            <div style={{
              marginTop: '16px',
              padding: '16px',
              backgroundColor: '#ffffff',
              border: '2px solid #dc2626',
              borderRadius: '8px',
              color: '#000000'
            }}>
              <p style={{
                color: '#dc2626',
                fontWeight: 'bold',
                fontSize: '14px',
                margin: '0 0 8px 0'
              }}>
                ❌ Errore nel caricamento del file:
              </p>
              <p style={{
                color: '#000000',
                fontSize: '14px',
                margin: '0',
                wordBreak: 'break-word'
              }}>{error}</p>
            </div>
          )}
        </div>
      )}
      {step === 'mapping' && (
        <MappingStep
          headers={headers}
          mapping={mapping}
          fields={fields}
          onChange={(header, value) => setMapping({ ...mapping, [header]: value })}
          onNext={() => setStep('layout')}
        />
      )}
      {step === 'layout' && (
        <div className="bg-white rounded-xl shadow px-6 py-8 text-center">
          <h2 className="text-xl font-semibold mb-4 text-indigo-700 dark:text-indigo-300">Choose template</h2>
          <LayoutStep selected={layout} onSelect={setLayout} />
          {error && (
            <div style={{
              marginTop: '16px',
              padding: '16px',
              backgroundColor: '#ffffff',
              border: '2px solid #dc2626',
              borderRadius: '8px',
              color: '#000000'
            }}>
              <p style={{
                color: '#dc2626',
                fontWeight: 'bold',
                fontSize: '14px',
                margin: '0 0 8px 0'
              }}>
                ❌ Errore durante la generazione:
              </p>
              <p style={{
                color: '#000000',
                fontSize: '14px',
                margin: '0',
                wordBreak: 'break-word'
              }}>{error}</p>
            </div>
          )}
          <button
            className="mt-6 px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
            onClick={handleGenerate}
          >
            Generate
          </button>
        </div>
      )}
    </div>
  );
}
