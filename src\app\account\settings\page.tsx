'use client'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '../../../lib/supabaseClient'
import { useAuth } from '../../../components/AuthProvider'

export default function SettingsPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [categories, setCategories] = useState<string[]>([])
  const [newCat, setNewCat] = useState('')

  useEffect(() => {
    if (!user) {
      router.push('/account/login')
      return
    }
    supabase
      .from('categories')
      .select('name')
      .eq('user_id', user.id)
      .then(({ data }) => {
        setCategories(data?.map((c) => c.name) || [])
      })
  }, [user, router])

  if (!user) return null

  async function addCategory(e: React.FormEvent) {
    e.preventDefault()
    if (!newCat || !user) return
    await supabase.from('categories').insert({ name: newCat, user_id: user.id })
    setCategories([...categories, newCat])
    setNewCat('')
  }

  return (
    <div className="w-full max-w-lg mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6 text-indigo-700">Settings</h1>
      <form onSubmit={addCategory} className="flex gap-2 mb-4">
        <input
          type="text"
          value={newCat}
          onChange={(e) => setNewCat(e.target.value)}
          placeholder="New category"
          className="border rounded-md px-3 py-2 flex-1"
        />
        <button type="submit" className="bg-indigo-600 text-white px-4 py-2 rounded-md">Add</button>
      </form>
      <ul className="list-disc pl-6">
        {categories.map((c) => (
          <li key={c}>{c}</li>
        ))}
        {categories.length === 0 && <p>No categories yet.</p>}
      </ul>
    </div>
  )
}
