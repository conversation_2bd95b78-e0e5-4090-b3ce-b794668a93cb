'use client';
import React from 'react';
import { LayoutType, ColorScheme, colorSchemes } from './types';
import { useInvoiceStore } from '../../store/invoiceStore';

// Static preview components to avoid loading issues
function getLayoutPreview(layout: LayoutType) {
  const previews = {
    classic: (
      <div className="w-full h-full bg-white p-2 text-xs border border-gray-200">
        <div className="flex justify-between items-start mb-2">
          <div>
            <div className="font-bold text-blue-600 text-sm">INVOICE</div>
            <div className="text-gray-500 text-xs">#INV-001</div>
          </div>
          <div className="w-4 h-4 bg-gray-200 rounded"></div>
        </div>
        <div className="bg-blue-50 p-1 rounded mb-2">
          <div className="text-xs font-semibold text-blue-700">Client Info</div>
          <div className="h-1 bg-blue-200 rounded mt-1"></div>
        </div>
        <div className="space-y-1 mb-2">
          <div className="flex justify-between">
            <div className="h-1 bg-gray-200 rounded w-1/2"></div>
            <div className="h-1 bg-gray-200 rounded w-1/4"></div>
          </div>
          <div className="flex justify-between">
            <div className="h-1 bg-gray-200 rounded w-2/3"></div>
            <div className="h-1 bg-gray-200 rounded w-1/5"></div>
          </div>
        </div>
        <div className="border-t border-gray-200 pt-1">
          <div className="flex justify-between">
            <div className="text-xs font-bold text-blue-600">Total</div>
            <div className="text-xs font-bold text-blue-600">€1,000</div>
          </div>
        </div>
      </div>
    ),
    modern: (
      <div className="w-full h-full bg-gradient-to-br from-blue-50 to-purple-50 p-2 text-xs relative overflow-hidden">
        <div className="absolute top-0 right-0 w-8 h-8 bg-gradient-to-br from-blue-200 to-purple-200 rounded-full opacity-50 -translate-y-2 translate-x-2"></div>
        <div className="relative z-10">
          <div className="font-bold text-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-sm mb-1">
            INVOICE
          </div>
          <div className="flex justify-between items-start mb-2">
            <div>
              <div className="text-xs text-gray-600">#INV-001</div>
              <div className="h-1 bg-gradient-to-r from-blue-200 to-purple-200 rounded w-8 mt-1"></div>
            </div>
            <div className="w-4 h-4 bg-gradient-to-br from-blue-200 to-purple-200 rounded"></div>
          </div>
          <div className="bg-white/70 backdrop-blur-sm p-1 rounded mb-2">
            <div className="h-1 bg-gradient-to-r from-blue-200 to-purple-200 rounded mb-1"></div>
            <div className="h-1 bg-gradient-to-r from-blue-200 to-purple-200 rounded w-3/4"></div>
          </div>
          <div className="space-y-1 mb-2">
            <div className="flex justify-between">
              <div className="h-1 bg-gradient-to-r from-blue-200 to-purple-200 rounded w-1/2"></div>
              <div className="h-1 bg-gradient-to-r from-blue-200 to-purple-200 rounded w-1/4"></div>
            </div>
          </div>
          <div className="bg-white/60 backdrop-blur-sm p-1 rounded">
            <div className="text-xs font-bold text-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text">€1,000</div>
          </div>
        </div>
      </div>
    ),
    minimal: (
      <div className="w-full h-full bg-white p-2 text-xs font-mono border border-gray-300">
        <div className="border-b border-gray-400 pb-1 mb-2">
          <div className="font-bold text-gray-800 text-sm tracking-wider">INVOICE</div>
          <div className="flex justify-between mt-1">
            <div>
              <div className="text-gray-500 text-xs">NO. INV-001</div>
              <div className="text-gray-500 text-xs">DATE 2024-01-15</div>
            </div>
            <div className="w-3 h-3 bg-gray-400 opacity-50"></div>
          </div>
        </div>
        <div className="mb-2">
          <div className="text-xs text-gray-400 tracking-wider mb-1">RECIPIENT</div>
          <div className="h-1 bg-gray-300 rounded mb-1"></div>
          <div className="h-1 bg-gray-300 rounded w-2/3"></div>
        </div>
        <div className="space-y-1 mb-2">
          <div className="flex justify-between text-xs">
            <div className="h-1 bg-gray-300 rounded w-1/2"></div>
            <div className="h-1 bg-gray-300 rounded w-1/4"></div>
          </div>
        </div>
        <div className="border-t border-gray-400 pt-1">
          <div className="flex justify-between">
            <div className="text-xs font-bold text-gray-800 tracking-wider">TOTAL</div>
            <div className="text-xs font-bold text-gray-800">€1,000</div>
          </div>
        </div>
      </div>
    )
  };

  return previews[layout] || previews.classic;
}

interface Props {
  selected: LayoutType | null;
  selectedColor?: ColorScheme;
  onSelect: (layout: LayoutType, colorScheme?: ColorScheme) => void;
  onProceed?: () => void;
}

export default function LayoutStep({ selected, selectedColor, onSelect, onProceed }: Props) {
  const { data } = useInvoiceStore();

  const handleColorSelect = (colorScheme: ColorScheme) => {
    if (selected) {
      onSelect(selected, colorScheme);
    }
  };

  return (
    <section className="w-full max-w-4xl mx-auto mt-12 bg-white/80 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl shadow-2xl px-6 py-8 sm:px-10 sm:py-12 flex flex-col items-center justify-center border border-gray-100 dark:border-gray-700">
      <h2 className="text-2xl font-semibold mb-6 text-center text-indigo-700">Pick your style</h2>

      {/* Layout Selection */}
      <div className="flex flex-wrap gap-4 justify-center mb-8">
        {(['classic', 'modern', 'minimal'] as LayoutType[]).map((layout) => (
          <button
            key={layout}
            className={`border-2 rounded-xl p-4 transition-all shadow-sm hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-400 ${
              selected === layout ? 'border-indigo-600' : 'border-gray-200'
            } w-36 sm:w-44 bg-white/60 dark:bg-gray-700/30 backdrop-blur`}
            onClick={() => onSelect(layout)}
          >
            <span className="block text-lg font-bold capitalize mb-2">{layout}</span>
            <div className="w-32 h-24 sm:w-40 sm:h-28 overflow-hidden rounded mb-1 bg-white dark:bg-gray-800 border border-gray-200">
              {getLayoutPreview(layout)}
            </div>
            <span className="text-xs text-gray-400">Preview</span>
          </button>
        ))}
      </div>

      {/* Color Selection - Only show if layout is selected */}
      {selected && (
        <>
          <h3 className="text-xl font-semibold mb-4 text-center text-indigo-700">Choose your color</h3>
          <div className="flex flex-wrap gap-3 justify-center mb-6">
            {(Object.keys(colorSchemes) as ColorScheme[]).map((colorScheme) => {
              const colors = colorSchemes[colorScheme];
              return (
                <button
                  key={colorScheme}
                  className={`border-2 rounded-lg p-3 transition-all shadow-sm hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-400 ${
                    selectedColor === colorScheme ? 'border-indigo-600' : 'border-gray-200'
                  } bg-white/60 dark:bg-gray-700/30 backdrop-blur min-w-[100px]`}
                  onClick={() => handleColorSelect(colorScheme)}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <div
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: colors.primary }}
                    />
                    <div
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: colors.secondary }}
                    />
                    <div
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: colors.accent }}
                    />
                  </div>
                  <span className="block text-sm font-medium capitalize">{colors.name}</span>
                </button>
              );
            })}
          </div>



          {/* Info message and Proceed Button - Only show if both layout and color are selected */}
          {selectedColor && onProceed && (
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600 mb-4">
                Perfect! You'll see your invoice with all your data in the next step.
              </p>
              <button
                className="px-6 py-3 rounded-full bg-indigo-600 text-white font-semibold hover:bg-indigo-700 shadow-md transition"
                onClick={onProceed}
              >
                Continue to Preview
              </button>
            </div>
          )}
        </>
      )}
    </section>
  );
}

