import type { InvoiceData } from "../../../store/invoiceStore";
import { NextRequest, NextResponse } from "next/server";
import { generatePdf } from "../../../lib/pdf/generatePdf";

export const runtime = "nodejs";

export async function POST(req: NextRequest) {
  console.log("🚀 [API/generate] Starting PDF generation request");

  try {
    const body = (await req.json()) as { data: InvoiceData; layout: string };
    console.log("📦 [API/generate] Request body received:", {
      hasData: !!body.data,
      layout: body.layout,
      clientData: body.data?.client,
      servicesCount: body.data?.services?.length || 0
    });

    // Validazione dei dati ricevuti
    if (!body.data) {
      console.error("❌ [API/generate] Missing invoice data");
      return NextResponse.json(
        { error: "Dati fattura mancanti", details: "I dati della fattura sono obbligatori" },
        { status: 400 }
      );
    }

    if (!body.layout) {
      console.error("❌ [API/generate] Missing layout");
      return NextResponse.json(
        { error: "Layout mancante", details: "Il layout della fattura è obbligatorio" },
        { status: 400 }
      );
    }

    // Additional validation
    if (!body.data.services || body.data.services.length === 0) {
      console.warn("⚠️ [API/generate] No services in invoice data");
    }

    console.log("🔄 [API/generate] Calling generatePdf...");
    const pdfBuffer = await generatePdf(body.data, body.layout);
    console.log("✅ [API/generate] PDF generated successfully, size:", pdfBuffer.length);

    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": "attachment; filename=invoice.pdf",
      },
    });
  } catch (e: unknown) {
    const error = e instanceof Error ? e : new Error(String(e));
    console.error("💥 [API/generate] PDF generation error:", error);
    console.error("💥 [API/generate] Error stack:", error.stack);

    // Fornisci messaggi di errore più specifici
    let errorMessage = "Errore generazione PDF";
    let details = error.message;

    if (error.message.includes('Chrome') || error.message.includes('chromium')) {
      errorMessage = "Errore del browser";
      details = "Problema nell'avvio del browser per la generazione PDF. Verifica che Chrome/Chromium sia installato.";
    } else if (error.message.includes('timeout')) {
      errorMessage = "Timeout";
      details = "La generazione del PDF ha richiesto troppo tempo";
    } else if (error.message.includes('network') || error.message.includes('fetch')) {
      errorMessage = "Errore di rete";
      details = "Problema di connessione durante la generazione";
    } else if (error.message.includes('ECONNREFUSED')) {
      errorMessage = "Errore di connessione";
      details = "Impossibile connettersi al server per la generazione PDF";
    }

    console.error("📤 [API/generate] Sending error response:", { errorMessage, details });

    return NextResponse.json(
      { error: errorMessage, details: details },
      { status: 500 }
    );
  }
}
