import type { InvoiceData } from "../../../store/invoiceStore";
import { NextRequest, NextResponse } from "next/server";
import { generatePdf } from "../../../lib/pdf/generatePdf";

export const runtime = "nodejs";

export async function POST(req: NextRequest) {
  try {
    const body = (await req.json()) as { data: InvoiceData; layout: string };

    // Validazione dei dati ricevuti
    if (!body.data) {
      return NextResponse.json(
        { error: "Dati fattura mancanti", details: "I dati della fattura sono obbligatori" },
        { status: 400 }
      );
    }

    if (!body.layout) {
      return NextResponse.json(
        { error: "Layout mancante", details: "Il layout della fattura è obbligatorio" },
        { status: 400 }
      );
    }

    const pdfBuffer = await generatePdf(body.data, body.layout);
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": "attachment; filename=invoice.pdf",
      },
    });
  } catch (e: unknown) {
    const error = e instanceof Error ? e : new Error(String(e));
    console.error("[API/generate] PDF generation error", error);

    // Fornisci messaggi di errore più specifici
    let errorMessage = "Errore generazione PDF";
    let details = error.message;

    if (error.message.includes('Chrome')) {
      errorMessage = "Errore del browser";
      details = "Problema nell'avvio del browser per la generazione PDF";
    } else if (error.message.includes('timeout')) {
      errorMessage = "Timeout";
      details = "La generazione del PDF ha richiesto troppo tempo";
    } else if (error.message.includes('network')) {
      errorMessage = "Errore di rete";
      details = "Problema di connessione durante la generazione";
    }

    return NextResponse.json(
      { error: errorMessage, details: details },
      { status: 500 }
    );
  }
}
