'use client';
import React, { useRef } from 'react';

interface Props {
  onFile: (file: File) => void;
}

export default function UploadStep({ onFile }: Props) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const files = e.dataTransfer.files;
    if (files.length > 0 && files[0].type === 'text/csv') {
      onFile(files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (f) onFile(f);
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="bg-white rounded-xl shadow-lg px-6 py-8 text-center border border-gray-200">
      <h1 className="text-3xl font-bold mb-6 text-indigo-700">CSV to Invoice</h1>
      <p className="text-gray-600 mb-8">Upload your CSV file to generate multiple invoices at once</p>

      <div
        className="border-2 border-dashed border-indigo-300 rounded-lg p-8 mb-6 bg-indigo-50 hover:bg-indigo-100 transition-colors cursor-pointer"
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleButtonClick}
      >
        <div className="flex flex-col items-center">
          <svg className="w-12 h-12 text-indigo-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          <p className="text-lg font-semibold text-indigo-700 mb-2">Drop your CSV file here</p>
          <p className="text-gray-500 mb-4">or click to browse</p>
          <button
            type="button"
            className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            onClick={handleButtonClick}
          >
            Choose CSV File
          </button>
        </div>
      </div>
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleFileSelect}
        className="hidden"
      />

      <div className="text-sm text-gray-500">
        <p>Supported format: CSV files only</p>
        <p>Make sure your CSV includes columns for client info, services, and amounts</p>
      </div>
    </div>
  );
}
