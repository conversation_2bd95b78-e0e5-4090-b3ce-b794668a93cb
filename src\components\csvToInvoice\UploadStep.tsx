'use client';
import React, { useRef } from 'react';

interface Props {
  onFile: (file: File) => void;
}

export default function UploadStep({ onFile }: Props) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      // Accetta sia text/csv che file con estensione .csv
      if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
        onFile(file);
      }
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (f) {
      // Reset il valore dell'input per permettere di selezionare lo stesso file di nuovo
      e.target.value = '';
      onFile(f);
    }
  };

  const handleButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    fileInputRef.current?.click();
  };

  return (
    <div style={{
      backgroundColor: '#ffffff',
      borderRadius: '12px',
      boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
      padding: '32px 24px',
      textAlign: 'center',
      border: '2px solid #000000',
      color: '#000000'
    }}>
      <h1 style={{
        fontSize: '32px',
        fontWeight: 'bold',
        marginBottom: '24px',
        color: '#000000'
      }}>
        📄 CSV to Invoice
      </h1>
      <p style={{
        color: '#000000',
        marginBottom: '32px',
        fontSize: '16px'
      }}>
        Upload your CSV file to generate multiple invoices at once
      </p>

      <div
        style={{
          border: '3px dashed #4f46e5',
          borderRadius: '8px',
          padding: '32px',
          marginBottom: '24px',
          backgroundColor: '#f8f9ff',
          cursor: 'pointer',
          transition: 'background-color 0.2s'
        }}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleButtonClick}
        onMouseOver={(e) => {
          e.currentTarget.style.backgroundColor = '#f0f0ff';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.backgroundColor = '#f8f9ff';
        }}
      >
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}>
          <svg style={{
            width: '48px',
            height: '48px',
            color: '#4f46e5',
            marginBottom: '16px'
          }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          <p style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#000000',
            marginBottom: '8px'
          }}>
            📁 Drop your CSV file here
          </p>
          <p style={{
            color: '#000000',
            marginBottom: '16px',
            fontSize: '14px'
          }}>
            or click anywhere to browse
          </p>
          <div style={{
            backgroundColor: '#4f46e5',
            color: '#ffffff',
            fontWeight: 'bold',
            padding: '12px 24px',
            borderRadius: '8px',
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            pointerEvents: 'none',
            fontSize: '16px'
          }}>
            Choose CSV File
          </div>
        </div>
      </div>
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />

      <div style={{
        fontSize: '14px',
        color: '#000000',
        lineHeight: '1.5'
      }}>
        <p style={{ margin: '4px 0' }}>✅ Supported format: CSV files only</p>
        <p style={{ margin: '4px 0' }}>💡 Make sure your CSV includes columns for client info, services, and amounts</p>
      </div>
    </div>
  );
}
