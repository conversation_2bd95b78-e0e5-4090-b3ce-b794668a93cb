This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Configurazione SMTP per il form di feedback

Per permettere l'invio dei feedback tramite email è necessario impostare alcune variabili nel file `.env`:

```
SMTP_HOST=your_smtp_host
SMTP_PORT=your_smtp_port
SMTP_USER=your_username
SMTP_PASS=your_password
SMTP_SECURE=true # opzionale, "true" per connessioni SSL
SMTP_FROM=<EMAIL> # opzionale, indirizzo mittente
```

Il feedback verrà inviato all'indirizzo `<EMAIL>`.

## Configurazione Supabase

Per abilitare l'autenticazione e il salvataggio delle fatture, è necessario configurare Supabase:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Schema del Database Supabase

Crea le seguenti tabelle nel tuo progetto Supabase:

```sql
-- Tabella per i profili utente
CREATE TABLE user_profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  company_name TEXT,
  address TEXT,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabella per le fatture salvate
CREATE TABLE saved_invoices (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  invoice_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Abilita RLS (Row Level Security)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_invoices ENABLE ROW LEVEL SECURITY;

-- Policy per user_profiles
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON user_profiles
  FOR ALL USING (auth.uid() = user_id);

-- Policy per saved_invoices
CREATE POLICY "Users can view own invoices" ON saved_invoices
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own invoices" ON saved_invoices
  FOR ALL USING (auth.uid() = user_id);
```

## Configurazione Blog

Il blog ora utilizza Supabase come database. La tabella `articles` è stata creata automaticamente durante la migrazione.

Per proteggere le operazioni di scrittura nel blog è necessario definire le seguenti variabili:

```
ADMIN_USERNAME=your_admin_username
ADMIN_PASSWORD=your_admin_password
ADMIN_TOKEN=your_admin_token
```

La service role key di Supabase è necessaria per le operazioni server-side del blog:

```
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Nuove Funzionalità

### Autenticazione Utente (Opzionale)
- **Login/Registrazione**: Gli utenti possono creare account per salvare le fatture
- **Dashboard**: Area personale per gestire le fatture salvate
- **Profilo**: Gestione delle informazioni personali e aziendali
- **Funzionamento senza autenticazione**: Tutte le funzionalità base rimangono gratuite e senza registrazione

### CSV to Invoice Migliorato
- **Interfaccia migliorata**: Pulsante di upload più visibile con drag & drop
- **Workflow completo**: Upload CSV → Mappatura campi → Selezione layout → Generazione PDF
- **Supporto batch**: Generazione di più fatture contemporaneamente

### Selezione Layout Avanzata
- **Anteprime visive**: Ogni template mostra un'anteprima del design
- **Schemi colore**: 3 varianti di colore per ogni layout (Ocean Blue, Forest Green, Royal Purple)
- **Template disponibili**: Classic, Modern, Minimal

### Informazioni di Pagamento
- **Campo dedicato**: Nuovo step nel wizard per inserire dettagli di pagamento
- **Integrazione template**: Le informazioni di pagamento appaiono in tutti i template
- **Flessibilità**: Campo opzionale, può essere saltato
- **Posizione nel flusso**: Inserito dopo i dettagli fattura e prima della selezione layout
- **Funzionalità Back**: Possibilità di tornare indietro al passo precedente

### Sistema di Feedback
- **Email funzionante**: Invio feedback tramite SMTP configurato
- **Destinatario**: I feedback vengono inviati a `<EMAIL>`
