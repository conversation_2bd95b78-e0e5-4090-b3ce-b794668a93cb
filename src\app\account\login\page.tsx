'use client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '../../../lib/supabaseClient'

export default function AccountLogin() {
  const router = useRouter()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    setError('')
    const { error } = await supabase.auth.signInWithPassword({ email, password })
    if (error) {
      setError(error.message)
    } else {
      router.push('/account/history')
    }
  }

  return (
    <div className="w-full max-w-sm mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-6 text-center text-indigo-700">Login</h1>
      <form onSubmit={handleSubmit} className="flex flex-col gap-4">
        <input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="border border-gray-300 rounded-md px-3 py-2"
        />
        <input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="border border-gray-300 rounded-md px-3 py-2"
        />
        <button
          type="submit"
          className="bg-indigo-600 hover:bg-indigo-700 text-white rounded-md px-4 py-2 font-semibold"
        >
          Login
        </button>
      </form>
      {error && <p className="text-red-600 mt-4 text-center">{error}</p>}
      <p className="mt-4 text-center">
        No account? <a href="/account/register" className="text-indigo-700">Register</a>
      </p>
    </div>
  )
}
